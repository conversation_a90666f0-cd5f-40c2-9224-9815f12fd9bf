'use client';
import React, { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, Button, Typography, Space, Spin, Alert, Result } from 'antd';
import { UserAddOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import Image from "next/image";
import logo from "@/app/images/logo.png";
import Hivechat from "@/app/images/hivechat.svg";
import { useSession } from 'next-auth/react';
import { validateInviteCode } from '../../admin/users/invite/actions';
import { joinWorkspaceByInvite } from './actions';

const { Title, Text } = Typography;

interface JoinWorkspacePageProps { }

const JoinWorkspacePage: React.FC<JoinWorkspacePageProps> = () => {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();

  const workspaceId = params.workspace as string;
  const inviteCode = params.inviteCode as string;

  const [loading, setLoading] = useState(true);
  const [joining, setJoining] = useState(false);
  const [inviteValid, setInviteValid] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // 验证邀请码
  const validateInvite = async () => {
    try {
      setLoading(true);
      const result = await validateInviteCode(workspaceId, inviteCode);

      if (result.valid) {
        setInviteValid(true);
        setError(null);
      } else {
        setInviteValid(false);
        setError(result.message || '邀请码无效');
      }
    } catch (error) {
      console.error('Validate invite error:', error);
      setError('验证邀请码时发生错误');
    } finally {
      setLoading(false);
    }
  };

  // 加入工作空间
  const handleJoinWorkspace = async () => {
    if (!session?.user?.id) {
      // 如果用户未登录，跳转到登录页面
      router.push(`/login?callbackUrl=${encodeURIComponent(window.location.href)}`);
      return;
    }

    try {
      setJoining(true);
      const result = await joinWorkspaceByInvite(workspaceId, inviteCode);

      if (result.status === 'success') {
        setSuccess(true);
        // 3秒后跳转到工作空间
        setTimeout(() => {
          router.push(`/${workspaceId}/chat`);
        }, 3000);
      } else {
        setError(result.message || '加入工作空间失败');
      }
    } catch (error) {
      console.error('Join workspace error:', error);
      setError('加入工作空间时发生错误');
    } finally {
      setJoining(false);
    }
  };

  useEffect(() => {
    if (workspaceId && inviteCode) {
      validateInvite();
    }
  }, [workspaceId, inviteCode]);

  // 如果用户认证状态还在加载中
  if (status === 'loading') {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  // 如果加入成功
  if (success) {
    return (
      <div
        className='login-page-bg'
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          padding: '20px'
        }}>
        <Result
          icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
          title="成功加入工作空间！"
          subTitle="正在跳转到聊天页面..."
          extra={
            <Button type="primary" onClick={() => router.push(`/${workspaceId}/chat`)}>
              立即前往
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div
      className='login-page-bg'
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        padding: '20px',
      }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 500,
          textAlign: 'center'
        }}
        loading={loading}
      >
        {loading ? (
          <div>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Text>验证邀请码中...</Text>
            </div>
          </div>
        ) : error ? (
          <Result
            icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
            title="邀请无效"
            subTitle={error}
            extra={
              <Button type="primary" onClick={() => router.push('/')}>
                返回首页
              </Button>
            }
          />
        ) : inviteValid ? (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div>
              <UserAddOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
              <Title level={3}>加入工作空间</Title>
              <Text type="secondary">
                您收到了加入工作空间的邀请
              </Text>
            </div>

            {!session ? (
              <div>
                <Alert
                  message="需要登录"
                  description="请先登录您的账户以加入工作空间"
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
                <Button
                  type="primary"
                  size="large"
                  onClick={() => router.push(`/auth/signin?callbackUrl=${encodeURIComponent(window.location.href)}`)}
                >
                  登录账户
                </Button>
              </div>
            ) : (
              <div>
                <Text>
                  您将以 <strong>{session.user.email}</strong> 的身份加入工作空间
                </Text>
                <div style={{ marginTop: 16 }}>
                  <Button
                    type="primary"
                    size="large"
                    loading={joining}
                    onClick={handleJoinWorkspace}
                  >
                    {joining ? '加入中...' : '确认加入'}
                  </Button>
                </div>
              </div>
            )}
          </Space>
        ) : null}
      </Card>
    </div>
  );
};

export default JoinWorkspacePage;
